import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_jailbreak_detection/flutter_jailbreak_detection.dart';
import 'package:root_detector/root_detector.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';

import '../models/threat_model.dart';
import '../models/scan_result_model.dart';
import '../models/device_info_model.dart';
import '../utils/logger.dart';
import '../constants/security_constants.dart';

class SecurityService {
  static final SecurityService _instance = SecurityService._internal();
  static SecurityService get instance => _instance;
  SecurityService._internal();

  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  final StreamController<ScanProgress> _scanProgressController = StreamController.broadcast();
  final StreamController<ThreatDetected> _threatController = StreamController.broadcast();
  
  Stream<ScanProgress> get scanProgress => _scanProgressController.stream;
  Stream<ThreatDetected> get threatDetected => _threatController.stream;
  
  bool _isInitialized = false;
  bool _isScanning = false;
  DeviceInfoModel? _deviceInfo;
  List<ThreatSignature> _threatSignatures = [];

  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _loadDeviceInfo();
      await _loadThreatSignatures();
      _isInitialized = true;
      Logger.info('SecurityService initialized successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize SecurityService', e, stackTrace);
      rethrow;
    }
  }

  Future<void> _loadDeviceInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        _deviceInfo = DeviceInfoModel(
          platform: 'Android',
          deviceId: androidInfo.id,
          model: androidInfo.model,
          manufacturer: androidInfo.manufacturer,
          osVersion: androidInfo.version.release,
          sdkVersion: androidInfo.version.sdkInt.toString(),
          isPhysicalDevice: androidInfo.isPhysicalDevice,
          appVersion: packageInfo.version,
          buildNumber: packageInfo.buildNumber,
        );
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        _deviceInfo = DeviceInfoModel(
          platform: 'iOS',
          deviceId: iosInfo.identifierForVendor ?? 'unknown',
          model: iosInfo.model,
          manufacturer: 'Apple',
          osVersion: iosInfo.systemVersion,
          sdkVersion: iosInfo.systemVersion,
          isPhysicalDevice: iosInfo.isPhysicalDevice,
          appVersion: packageInfo.version,
          buildNumber: packageInfo.buildNumber,
        );
      }
    } catch (e, stackTrace) {
      Logger.error('Failed to load device info', e, stackTrace);
    }
  }

  Future<void> _loadThreatSignatures() async {
    try {
      // Load threat signatures from assets or API
      _threatSignatures = SecurityConstants.defaultThreatSignatures;
      Logger.info('Loaded ${_threatSignatures.length} threat signatures');
    } catch (e, stackTrace) {
      Logger.error('Failed to load threat signatures', e, stackTrace);
    }
  }

  Future<ScanResultModel> performQuickScan() async {
    if (_isScanning) {
      throw Exception('Scan already in progress');
    }

    _isScanning = true;
    final scanId = DateTime.now().millisecondsSinceEpoch.toString();
    final startTime = DateTime.now();
    
    try {
      _scanProgressController.add(ScanProgress(
        scanId: scanId,
        progress: 0.0,
        currentTask: 'Initializing scan...',
        threatsFound: 0,
      ));

      final List<ThreatModel> threats = [];
      
      // Device integrity check
      _scanProgressController.add(ScanProgress(
        scanId: scanId,
        progress: 0.1,
        currentTask: 'Checking device integrity...',
        threatsFound: threats.length,
      ));
      
      final deviceThreats = await _checkDeviceIntegrity();
      threats.addAll(deviceThreats);

      // App analysis
      _scanProgressController.add(ScanProgress(
        scanId: scanId,
        progress: 0.3,
        currentTask: 'Analyzing installed apps...',
        threatsFound: threats.length,
      ));
      
      final appThreats = await _analyzeInstalledApps();
      threats.addAll(appThreats);

      // File system scan
      _scanProgressController.add(ScanProgress(
        scanId: scanId,
        progress: 0.5,
        currentTask: 'Scanning file system...',
        threatsFound: threats.length,
      ));
      
      final fileThreats = await _scanFileSystem();
      threats.addAll(fileThreats);

      // Network security check
      _scanProgressController.add(ScanProgress(
        scanId: scanId,
        progress: 0.7,
        currentTask: 'Checking network security...',
        threatsFound: threats.length,
      ));
      
      final networkThreats = await _checkNetworkSecurity();
      threats.addAll(networkThreats);

      // System configuration check
      _scanProgressController.add(ScanProgress(
        scanId: scanId,
        progress: 0.9,
        currentTask: 'Checking system configuration...',
        threatsFound: threats.length,
      ));
      
      final systemThreats = await _checkSystemConfiguration();
      threats.addAll(systemThreats);

      // Complete scan
      _scanProgressController.add(ScanProgress(
        scanId: scanId,
        progress: 1.0,
        currentTask: 'Scan completed',
        threatsFound: threats.length,
      ));

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      final result = ScanResultModel(
        scanId: scanId,
        startTime: startTime,
        endTime: endTime,
        duration: duration,
        threatsFound: threats,
        deviceInfo: _deviceInfo!,
        scanType: ScanType.quick,
        overallRiskScore: _calculateRiskScore(threats),
      );

      // Notify about critical threats
      for (final threat in threats) {
        if (threat.severity == ThreatSeverity.critical) {
          _threatController.add(ThreatDetected(threat: threat, scanId: scanId));
        }
      }

      return result;
    } finally {
      _isScanning = false;
    }
  }

  Future<List<ThreatModel>> _checkDeviceIntegrity() async {
    final threats = <ThreatModel>[];
    
    try {
      // Check for root/jailbreak
      bool isCompromised = false;
      String compromiseType = '';
      
      if (Platform.isAndroid) {
        isCompromised = await RootDetector.isRooted;
        compromiseType = 'rooted';
      } else if (Platform.isIOS) {
        isCompromised = await FlutterJailbreakDetection.jailbroken;
        compromiseType = 'jailbroken';
      }
      
      if (isCompromised) {
        threats.add(ThreatModel(
          id: 'device_compromised',
          name: 'Device Compromised',
          description: 'Device appears to be $compromiseType',
          severity: ThreatSeverity.critical,
          category: ThreatCategory.deviceIntegrity,
          detectedAt: DateTime.now(),
          filePath: null,
          riskScore: 95,
          recommendations: [
            'Use a non-$compromiseType device for sensitive operations',
            'Consider factory reset if compromise was unintentional',
            'Avoid installing apps from unknown sources',
          ],
        ));
      }

      // Check debug mode
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        if (!androidInfo.isPhysicalDevice) {
          threats.add(ThreatModel(
            id: 'emulator_detected',
            name: 'Emulator Detected',
            description: 'App is running on an emulator',
            severity: ThreatSeverity.medium,
            category: ThreatCategory.deviceIntegrity,
            detectedAt: DateTime.now(),
            filePath: null,
            riskScore: 40,
            recommendations: [
              'Use a physical device for better security',
              'Emulators may have security vulnerabilities',
            ],
          ));
        }
      }
      
    } catch (e, stackTrace) {
      Logger.error('Error checking device integrity', e, stackTrace);
    }
    
    return threats;
  }

  Future<List<ThreatModel>> _analyzeInstalledApps() async {
    final threats = <ThreatModel>[];
    
    try {
      // This would require platform-specific implementation
      // For now, we'll simulate app analysis
      
      // Check for suspicious app patterns
      final suspiciousApps = await _getSuspiciousApps();
      
      for (final app in suspiciousApps) {
        threats.add(ThreatModel(
          id: 'suspicious_app_${app.packageName}',
          name: 'Suspicious App Detected',
          description: 'App "${app.name}" has suspicious characteristics',
          severity: ThreatSeverity.high,
          category: ThreatCategory.maliciousApp,
          detectedAt: DateTime.now(),
          filePath: app.packageName,
          riskScore: 75,
          recommendations: [
            'Uninstall the suspicious app',
            'Check app permissions',
            'Install apps only from official stores',
          ],
        ));
      }
      
    } catch (e, stackTrace) {
      Logger.error('Error analyzing installed apps', e, stackTrace);
    }
    
    return threats;
  }

  Future<List<ThreatModel>> _scanFileSystem() async {
    final threats = <ThreatModel>[];
    
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final files = await _getFilesRecursively(appDir);
      
      for (final file in files) {
        final threat = await _analyzeFile(file);
        if (threat != null) {
          threats.add(threat);
        }
      }
      
    } catch (e, stackTrace) {
      Logger.error('Error scanning file system', e, stackTrace);
    }
    
    return threats;
  }

  Future<List<ThreatModel>> _checkNetworkSecurity() async {
    final threats = <ThreatModel>[];
    
    try {
      // Check for insecure network configurations
      // This would require platform-specific implementation
      
      // Simulate network security check
      final hasInsecureConnections = await _checkInsecureConnections();
      
      if (hasInsecureConnections) {
        threats.add(ThreatModel(
          id: 'insecure_network',
          name: 'Insecure Network Connection',
          description: 'App uses insecure network connections',
          severity: ThreatSeverity.medium,
          category: ThreatCategory.networkSecurity,
          detectedAt: DateTime.now(),
          filePath: null,
          riskScore: 50,
          recommendations: [
            'Use HTTPS for all network communications',
            'Enable certificate pinning',
            'Avoid public WiFi for sensitive operations',
          ],
        ));
      }
      
    } catch (e, stackTrace) {
      Logger.error('Error checking network security', e, stackTrace);
    }
    
    return threats;
  }

  Future<List<ThreatModel>> _checkSystemConfiguration() async {
    final threats = <ThreatModel>[];
    
    try {
      // Check system security settings
      // This would require platform-specific implementation
      
    } catch (e, stackTrace) {
      Logger.error('Error checking system configuration', e, stackTrace);
    }
    
    return threats;
  }

  Future<List<AppInfo>> _getSuspiciousApps() async {
    // Platform-specific implementation needed
    return [];
  }

  Future<List<File>> _getFilesRecursively(Directory dir) async {
    final files = <File>[];
    
    try {
      await for (final entity in dir.list(recursive: true)) {
        if (entity is File) {
          files.add(entity);
        }
      }
    } catch (e) {
      Logger.warning('Cannot access directory: ${dir.path}');
    }
    
    return files;
  }

  Future<ThreatModel?> _analyzeFile(File file) async {
    try {
      final content = await file.readAsBytes();
      final hash = sha256.convert(content).toString();
      
      // Check against threat signatures
      for (final signature in _threatSignatures) {
        if (signature.matches(content, file.path)) {
          return ThreatModel(
            id: 'malware_${hash.substring(0, 8)}',
            name: signature.name,
            description: signature.description,
            severity: signature.severity,
            category: ThreatCategory.malware,
            detectedAt: DateTime.now(),
            filePath: file.path,
            riskScore: signature.riskScore,
            recommendations: signature.recommendations,
          );
        }
      }
      
    } catch (e) {
      // File might be inaccessible
    }
    
    return null;
  }

  Future<bool> _checkInsecureConnections() async {
    // Simulate network security check
    return false;
  }

  int _calculateRiskScore(List<ThreatModel> threats) {
    if (threats.isEmpty) return 0;
    
    int totalScore = 0;
    for (final threat in threats) {
      totalScore += threat.riskScore;
    }
    
    return (totalScore / threats.length).round().clamp(0, 100);
  }

  DeviceInfoModel? get deviceInfo => _deviceInfo;
  bool get isScanning => _isScanning;

  void dispose() {
    _scanProgressController.close();
    _threatController.close();
  }
}

class ScanProgress {
  final String scanId;
  final double progress;
  final String currentTask;
  final int threatsFound;

  ScanProgress({
    required this.scanId,
    required this.progress,
    required this.currentTask,
    required this.threatsFound,
  });
}

class ThreatDetected {
  final ThreatModel threat;
  final String scanId;

  ThreatDetected({
    required this.threat,
    required this.scanId,
  });
}

class AppInfo {
  final String packageName;
  final String name;
  final String version;
  final List<String> permissions;

  AppInfo({
    required this.packageName,
    required this.name,
    required this.version,
    required this.permissions,
  });
}

class ThreatSignature {
  final String name;
  final String description;
  final ThreatSeverity severity;
  final int riskScore;
  final List<String> recommendations;
  final List<int> signature;
  final List<String> filePatterns;

  ThreatSignature({
    required this.name,
    required this.description,
    required this.severity,
    required this.riskScore,
    required this.recommendations,
    required this.signature,
    required this.filePatterns,
  });

  bool matches(List<int> content, String filePath) {
    // Check file pattern
    for (final pattern in filePatterns) {
      if (filePath.contains(pattern)) {
        return true;
      }
    }
    
    // Check content signature
    if (signature.isNotEmpty && content.length >= signature.length) {
      for (int i = 0; i <= content.length - signature.length; i++) {
        bool match = true;
        for (int j = 0; j < signature.length; j++) {
          if (content[i + j] != signature[j]) {
            match = false;
            break;
          }
        }
        if (match) return true;
      }
    }
    
    return false;
  }
}
