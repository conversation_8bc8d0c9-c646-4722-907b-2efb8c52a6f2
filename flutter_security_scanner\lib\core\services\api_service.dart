import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../models/api_response.dart';
import '../models/scan_result_model.dart';
import '../models/threat_model.dart';
import '../models/device_info_model.dart';
import '../utils/logger.dart';
import '../constants/api_constants.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  static ApiService get instance => _instance;
  ApiService._internal();

  late Dio _dio;
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final Connectivity _connectivity = Connectivity();
  
  String? _apiKey;
  String? _deviceId;
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadCredentials();
      _setupDio();
      await _registerDevice();
      _isInitialized = true;
      Logger.info('ApiService initialized successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize ApiService', e, stackTrace);
    }
  }

  Future<void> _loadCredentials() async {
    _apiKey = await _secureStorage.read(key: 'api_key') ?? ApiConstants.defaultApiKey;
    _deviceId = await _secureStorage.read(key: 'device_id') ?? await _generateDeviceId();
    
    // Save device ID if it was generated
    if (await _secureStorage.read(key: 'device_id') == null) {
      await _secureStorage.write(key: 'device_id', value: _deviceId);
    }
  }

  Future<String> _generateDeviceId() async {
    final deviceInfo = DeviceInfoPlugin();
    
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      return 'android_${androidInfo.id}';
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return 'ios_${iosInfo.identifierForVendor ?? DateTime.now().millisecondsSinceEpoch.toString()}';
    }
    
    return 'unknown_${DateTime.now().millisecondsSinceEpoch}';
  }

  void _setupDio() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConstants.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': _apiKey,
        'X-Device-ID': _deviceId,
        'User-Agent': 'SecurityScanner-Flutter/1.0.0',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) => Logger.debug('API: $object'),
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Check connectivity
        final connectivityResult = await _connectivity.checkConnectivity();
        if (connectivityResult == ConnectivityResult.none) {
          handler.reject(DioException(
            requestOptions: options,
            error: 'No internet connection',
            type: DioExceptionType.connectionError,
          ));
          return;
        }
        
        handler.next(options);
      },
      onError: (error, handler) async {
        Logger.error('API Error: ${error.message}', error);
        
        // Handle specific error cases
        if (error.response?.statusCode == 401) {
          await _handleUnauthorized();
        } else if (error.response?.statusCode == 429) {
          await _handleRateLimit();
        }
        
        handler.next(error);
      },
    ));
  }

  Future<void> _registerDevice() async {
    try {
      final deviceInfo = await _getDeviceInfo();
      final packageInfo = await PackageInfo.fromPlatform();
      
      final response = await _dio.post('/device/register', data: {
        'device_id': _deviceId,
        'platform': deviceInfo.platform,
        'model': deviceInfo.model,
        'os_version': deviceInfo.osVersion,
        'app_version': packageInfo.version,
        'build_number': packageInfo.buildNumber,
        'manufacturer': deviceInfo.manufacturer,
        'is_physical_device': deviceInfo.isPhysicalDevice,
      });
      
      if (response.statusCode == 200) {
        Logger.info('Device registered successfully');
      }
    } catch (e) {
      Logger.warning('Failed to register device: $e');
    }
  }

  Future<DeviceInfoModel> _getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();
    final packageInfo = await PackageInfo.fromPlatform();
    
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      return DeviceInfoModel(
        platform: 'Android',
        deviceId: androidInfo.id,
        model: androidInfo.model,
        manufacturer: androidInfo.manufacturer,
        osVersion: androidInfo.version.release,
        sdkVersion: androidInfo.version.sdkInt.toString(),
        isPhysicalDevice: androidInfo.isPhysicalDevice,
        appVersion: packageInfo.version,
        buildNumber: packageInfo.buildNumber,
      );
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return DeviceInfoModel(
        platform: 'iOS',
        deviceId: iosInfo.identifierForVendor ?? 'unknown',
        model: iosInfo.model,
        manufacturer: 'Apple',
        osVersion: iosInfo.systemVersion,
        sdkVersion: iosInfo.systemVersion,
        isPhysicalDevice: iosInfo.isPhysicalDevice,
        appVersion: packageInfo.version,
        buildNumber: packageInfo.buildNumber,
      );
    }
    
    throw UnsupportedError('Unsupported platform');
  }

  Future<ApiResponse<Map<String, dynamic>>> startScan({
    required String scanType,
    String? directory,
  }) async {
    try {
      final response = await _dio.post('/scan/start', data: {
        'type': scanType,
        'directory': directory ?? './',
        'device_id': _deviceId,
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      return ApiResponse.success(response.data['data']);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  Future<ApiResponse<List<ThreatModel>>> getScanResults({
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final response = await _dio.get('/scan/results', queryParameters: {
        'limit': limit,
        'offset': offset,
        'device_id': _deviceId,
      });
      
      final data = response.data['data'];
      final threats = (data['results'] as List)
          .map((json) => ThreatModel.fromJson(json))
          .toList();
      
      return ApiResponse.success(threats);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> uploadScanResults(
    ScanResultModel scanResult,
  ) async {
    try {
      final response = await _dio.post('/scan/upload', data: {
        'scan_id': scanResult.scanId,
        'device_id': _deviceId,
        'start_time': scanResult.startTime.toIso8601String(),
        'end_time': scanResult.endTime.toIso8601String(),
        'duration_ms': scanResult.duration.inMilliseconds,
        'scan_type': scanResult.scanType.toString(),
        'threats_found': scanResult.threatsFound.map((t) => t.toJson()).toList(),
        'overall_risk_score': scanResult.overallRiskScore,
        'device_info': scanResult.deviceInfo.toJson(),
      });
      
      return ApiResponse.success(response.data['data']);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getFileChanges({
    int limit = 20,
  }) async {
    try {
      final response = await _dio.get('/monitor/changes', queryParameters: {
        'limit': limit,
        'device_id': _deviceId,
      });
      
      final data = response.data['data'];
      final changes = List<Map<String, dynamic>>.from(data['changes']);
      
      return ApiResponse.success(changes);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> createBaseline({
    List<String>? directories,
  }) async {
    try {
      final response = await _dio.post('/monitor/baseline', data: {
        'directories': directories ?? ['./'],
        'device_id': _deviceId,
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      return ApiResponse.success(response.data['data']);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> analyzeFile(String filePath) async {
    try {
      final response = await _dio.post('/scan/file', data: {
        'file_path': filePath,
        'device_id': _deviceId,
      });
      
      return ApiResponse.success(response.data['data']);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> quarantineFile(String filePath) async {
    try {
      final response = await _dio.post('/file/quarantine', data: {
        'file_path': filePath,
        'device_id': _deviceId,
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      return ApiResponse.success(response.data['data']);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> sendAlert({
    required String event,
    required String severity,
    Map<String, dynamic>? details,
  }) async {
    try {
      final response = await _dio.post('/alerts/send', data: {
        'event': event,
        'severity': severity,
        'details': details ?? {},
        'device_id': _deviceId,
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      return ApiResponse.success(response.data['data']);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getScanStatus() async {
    try {
      final response = await _dio.get('/scan/status', queryParameters: {
        'device_id': _deviceId,
      });
      
      return ApiResponse.success(response.data['data']);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getThreatSummary() async {
    try {
      final response = await _dio.get('/threats/summary', queryParameters: {
        'device_id': _deviceId,
      });
      
      return ApiResponse.success(response.data['data']);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getLatestReport() async {
    try {
      final response = await _dio.get('/reports/latest', queryParameters: {
        'device_id': _deviceId,
      });
      
      return ApiResponse.success(response.data['data']);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> updateThreatDatabase() async {
    try {
      final response = await _dio.get('/threats/database', queryParameters: {
        'device_id': _deviceId,
        'last_update': await _getLastThreatUpdate(),
      });
      
      final data = response.data['data'];
      final threats = List<Map<String, dynamic>>.from(data['threats']);
      
      // Save last update timestamp
      await _saveLastThreatUpdate(data['timestamp']);
      
      return ApiResponse.success(threats);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  Future<String?> _getLastThreatUpdate() async {
    return await _secureStorage.read(key: 'last_threat_update');
  }

  Future<void> _saveLastThreatUpdate(String timestamp) async {
    await _secureStorage.write(key: 'last_threat_update', value: timestamp);
  }

  Future<void> _handleUnauthorized() async {
    Logger.warning('API unauthorized - refreshing credentials');
    // Implement token refresh logic if needed
  }

  Future<void> _handleRateLimit() async {
    Logger.warning('API rate limit exceeded');
    // Implement rate limit handling
  }

  String _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Connection timeout. Please check your internet connection.';
        case DioExceptionType.connectionError:
          return 'Connection error. Please check your internet connection.';
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['error']?['message'];
          return message ?? 'Server error ($statusCode)';
        case DioExceptionType.cancel:
          return 'Request was cancelled';
        case DioExceptionType.unknown:
          return 'An unexpected error occurred';
        default:
          return 'Network error occurred';
      }
    }
    
    return error.toString();
  }

  Future<bool> isConnected() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  void dispose() {
    _dio.close();
  }
}
