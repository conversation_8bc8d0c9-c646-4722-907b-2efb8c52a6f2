import 'dart:io';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_jailbreak_detection/flutter_jailbreak_detection.dart';
import 'package:root_detector/root_detector.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../models/device_security_result.dart';
import '../models/threat_model.dart';
import '../utils/logger.dart';

class DeviceSecurityService {
  static final DeviceSecurityService _instance = DeviceSecurityService._internal();
  static DeviceSecurityService get instance => _instance;
  DeviceSecurityService._internal();

  static const MethodChannel _channel = MethodChannel('security_scanner/device_security');
  
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  Future<DeviceSecurityResult> performDeviceSecurityCheck() async {
    final List<ThreatModel> threats = [];
    final Map<String, dynamic> securityChecks = {};
    
    try {
      // Root/Jailbreak Detection
      final rootJailbreakResult = await _checkRootJailbreak();
      securityChecks['root_jailbreak'] = rootJailbreakResult;
      if (rootJailbreakResult['isCompromised']) {
        threats.add(_createRootJailbreakThreat(rootJailbreakResult));
      }
      
      // Debug Detection
      final debugResult = await _checkDebugMode();
      securityChecks['debug_mode'] = debugResult;
      if (debugResult['isEnabled']) {
        threats.add(_createDebugThreat(debugResult));
      }
      
      // Emulator Detection
      final emulatorResult = await _checkEmulator();
      securityChecks['emulator'] = emulatorResult;
      if (emulatorResult['isEmulator']) {
        threats.add(_createEmulatorThreat(emulatorResult));
      }
      
      // App Integrity Check
      final integrityResult = await _checkAppIntegrity();
      securityChecks['app_integrity'] = integrityResult;
      if (!integrityResult['isValid']) {
        threats.add(_createIntegrityThreat(integrityResult));
      }
      
      // Permission Analysis
      final permissionResult = await _analyzePermissions();
      securityChecks['permissions'] = permissionResult;
      if (permissionResult['hasDangerousPermissions']) {
        threats.add(_createPermissionThreat(permissionResult));
      }
      
      // System Security Settings
      final systemResult = await _checkSystemSecurity();
      securityChecks['system_security'] = systemResult;
      threats.addAll(_createSystemSecurityThreats(systemResult));
      
      // Network Security
      final networkResult = await _checkNetworkSecurity();
      securityChecks['network_security'] = networkResult;
      if (networkResult['hasInsecureConnections']) {
        threats.add(_createNetworkThreat(networkResult));
      }
      
      // Certificate Validation
      final certResult = await _checkCertificates();
      securityChecks['certificates'] = certResult;
      if (certResult['hasInvalidCerts']) {
        threats.add(_createCertificateThreat(certResult));
      }
      
    } catch (e, stackTrace) {
      Logger.error('Error performing device security check', e, stackTrace);
    }
    
    return DeviceSecurityResult(
      threats: threats,
      securityChecks: securityChecks,
      overallRiskScore: _calculateOverallRisk(threats),
      timestamp: DateTime.now(),
    );
  }
  
  Future<Map<String, dynamic>> _checkRootJailbreak() async {
    try {
      bool isCompromised = false;
      String method = '';
      List<String> indicators = [];
      
      if (Platform.isAndroid) {
        // Multiple root detection methods
        final isRooted = await RootDetector.isRooted;
        if (isRooted) {
          isCompromised = true;
          method = 'root';
          indicators.add('Root access detected');
        }
        
        // Check for root management apps
        final rootApps = await _checkRootApps();
        if (rootApps.isNotEmpty) {
          isCompromised = true;
          indicators.addAll(rootApps);
        }
        
        // Check for su binary
        final hasSuBinary = await _checkSuBinary();
        if (hasSuBinary) {
          isCompromised = true;
          indicators.add('Su binary found');
        }
        
        // Check build tags
        final hasDangerousBuildTags = await _checkBuildTags();
        if (hasDangerousBuildTags) {
          isCompromised = true;
          indicators.add('Dangerous build tags detected');
        }
        
      } else if (Platform.isIOS) {
        final isJailbroken = await FlutterJailbreakDetection.jailbroken;
        if (isJailbroken) {
          isCompromised = true;
          method = 'jailbreak';
          indicators.add('Jailbreak detected');
        }
        
        // Check for jailbreak apps
        final jailbreakApps = await _checkJailbreakApps();
        if (jailbreakApps.isNotEmpty) {
          isCompromised = true;
          indicators.addAll(jailbreakApps);
        }
        
        // Check for suspicious files
        final suspiciousFiles = await _checkSuspiciousFiles();
        if (suspiciousFiles.isNotEmpty) {
          isCompromised = true;
          indicators.addAll(suspiciousFiles);
        }
      }
      
      return {
        'isCompromised': isCompromised,
        'method': method,
        'indicators': indicators,
        'riskScore': isCompromised ? 95 : 0,
      };
    } catch (e) {
      Logger.error('Error checking root/jailbreak', e);
      return {
        'isCompromised': false,
        'method': '',
        'indicators': <String>[],
        'riskScore': 0,
        'error': e.toString(),
      };
    }
  }
  
  Future<List<String>> _checkRootApps() async {
    try {
      final result = await _channel.invokeMethod('checkRootApps');
      return List<String>.from(result ?? []);
    } catch (e) {
      return [];
    }
  }
  
  Future<bool> _checkSuBinary() async {
    try {
      final result = await _channel.invokeMethod('checkSuBinary');
      return result ?? false;
    } catch (e) {
      return false;
    }
  }
  
  Future<bool> _checkBuildTags() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return androidInfo.tags.contains('test-keys');
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  
  Future<List<String>> _checkJailbreakApps() async {
    try {
      final result = await _channel.invokeMethod('checkJailbreakApps');
      return List<String>.from(result ?? []);
    } catch (e) {
      return [];
    }
  }
  
  Future<List<String>> _checkSuspiciousFiles() async {
    try {
      final result = await _channel.invokeMethod('checkSuspiciousFiles');
      return List<String>.from(result ?? []);
    } catch (e) {
      return [];
    }
  }
  
  Future<Map<String, dynamic>> _checkDebugMode() async {
    try {
      bool isEnabled = false;
      String type = '';
      
      if (Platform.isAndroid) {
        final result = await _channel.invokeMethod('checkDebugMode');
        isEnabled = result['isEnabled'] ?? false;
        type = result['type'] ?? '';
      } else if (Platform.isIOS) {
        // iOS debug detection
        final result = await _channel.invokeMethod('checkDebugMode');
        isEnabled = result['isEnabled'] ?? false;
        type = result['type'] ?? '';
      }
      
      return {
        'isEnabled': isEnabled,
        'type': type,
        'riskScore': isEnabled ? 30 : 0,
      };
    } catch (e) {
      return {
        'isEnabled': false,
        'type': '',
        'riskScore': 0,
      };
    }
  }
  
  Future<Map<String, dynamic>> _checkEmulator() async {
    try {
      bool isEmulator = false;
      String type = '';
      
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        isEmulator = !androidInfo.isPhysicalDevice;
        
        // Additional emulator checks
        final emulatorIndicators = [
          androidInfo.brand.toLowerCase().contains('generic'),
          androidInfo.device.toLowerCase().contains('generic'),
          androidInfo.product.toLowerCase().contains('sdk'),
          androidInfo.hardware.toLowerCase().contains('goldfish'),
          androidInfo.hardware.toLowerCase().contains('ranchu'),
        ];
        
        if (emulatorIndicators.any((indicator) => indicator)) {
          isEmulator = true;
          type = 'Android Emulator';
        }
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        isEmulator = !iosInfo.isPhysicalDevice;
        if (isEmulator) {
          type = 'iOS Simulator';
        }
      }
      
      return {
        'isEmulator': isEmulator,
        'type': type,
        'riskScore': isEmulator ? 40 : 0,
      };
    } catch (e) {
      return {
        'isEmulator': false,
        'type': '',
        'riskScore': 0,
      };
    }
  }
  
  Future<Map<String, dynamic>> _checkAppIntegrity() async {
    try {
      final result = await _channel.invokeMethod('checkAppIntegrity');
      return {
        'isValid': result['isValid'] ?? true,
        'signatureValid': result['signatureValid'] ?? true,
        'checksumValid': result['checksumValid'] ?? true,
        'riskScore': (result['isValid'] ?? true) ? 0 : 70,
      };
    } catch (e) {
      return {
        'isValid': true,
        'signatureValid': true,
        'checksumValid': true,
        'riskScore': 0,
      };
    }
  }
  
  Future<Map<String, dynamic>> _analyzePermissions() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final dangerousPermissions = <String>[];
      final allPermissions = <String>[];
      
      // Check dangerous permissions
      final permissions = [
        Permission.camera,
        Permission.microphone,
        Permission.location,
        Permission.contacts,
        Permission.sms,
        Permission.phone,
        Permission.storage,
        Permission.manageExternalStorage,
        Permission.systemAlertWindow,
        Permission.accessNotificationPolicy,
      ];
      
      for (final permission in permissions) {
        final status = await permission.status;
        allPermissions.add(permission.toString());
        
        if (status.isGranted && _isDangerousPermission(permission)) {
          dangerousPermissions.add(permission.toString());
        }
      }
      
      return {
        'hasDangerousPermissions': dangerousPermissions.isNotEmpty,
        'dangerousPermissions': dangerousPermissions,
        'allPermissions': allPermissions,
        'riskScore': dangerousPermissions.length * 10,
      };
    } catch (e) {
      return {
        'hasDangerousPermissions': false,
        'dangerousPermissions': <String>[],
        'allPermissions': <String>[],
        'riskScore': 0,
      };
    }
  }
  
  bool _isDangerousPermission(Permission permission) {
    const dangerousPermissions = [
      Permission.systemAlertWindow,
      Permission.manageExternalStorage,
      Permission.accessNotificationPolicy,
    ];
    return dangerousPermissions.contains(permission);
  }
  
  Future<Map<String, dynamic>> _checkSystemSecurity() async {
    try {
      final result = await _channel.invokeMethod('checkSystemSecurity');
      return result ?? {
        'screenLockEnabled': true,
        'encryptionEnabled': true,
        'unknownSourcesEnabled': false,
        'riskScore': 0,
      };
    } catch (e) {
      return {
        'screenLockEnabled': true,
        'encryptionEnabled': true,
        'unknownSourcesEnabled': false,
        'riskScore': 0,
      };
    }
  }
  
  Future<Map<String, dynamic>> _checkNetworkSecurity() async {
    try {
      final result = await _channel.invokeMethod('checkNetworkSecurity');
      return result ?? {
        'hasInsecureConnections': false,
        'certificatePinningEnabled': true,
        'riskScore': 0,
      };
    } catch (e) {
      return {
        'hasInsecureConnections': false,
        'certificatePinningEnabled': true,
        'riskScore': 0,
      };
    }
  }
  
  Future<Map<String, dynamic>> _checkCertificates() async {
    try {
      final result = await _channel.invokeMethod('checkCertificates');
      return result ?? {
        'hasInvalidCerts': false,
        'userCertsInstalled': false,
        'riskScore': 0,
      };
    } catch (e) {
      return {
        'hasInvalidCerts': false,
        'userCertsInstalled': false,
        'riskScore': 0,
      };
    }
  }
  
  ThreatModel _createRootJailbreakThreat(Map<String, dynamic> result) {
    final method = result['method'] as String;
    final indicators = List<String>.from(result['indicators']);
    
    return ThreatModel(
      id: 'device_compromised',
      name: 'Device Compromised',
      description: 'Device appears to be ${method}ed. This significantly reduces security.',
      severity: ThreatSeverity.critical,
      category: ThreatCategory.deviceIntegrity,
      detectedAt: DateTime.now(),
      filePath: null,
      riskScore: result['riskScore'],
      recommendations: [
        'Use a non-${method}ed device for sensitive operations',
        'Consider factory reset if compromise was unintentional',
        'Avoid installing apps from unknown sources',
        'Enable additional security measures',
      ],
      additionalInfo: {
        'method': method,
        'indicators': indicators,
      },
    );
  }
  
  ThreatModel _createDebugThreat(Map<String, dynamic> result) {
    return ThreatModel(
      id: 'debug_enabled',
      name: 'Debug Mode Enabled',
      description: 'Debug mode is enabled, which can be exploited by malicious apps.',
      severity: ThreatSeverity.medium,
      category: ThreatCategory.systemConfiguration,
      detectedAt: DateTime.now(),
      filePath: null,
      riskScore: result['riskScore'],
      recommendations: [
        'Disable USB debugging when not needed',
        'Only enable debug mode when developing',
        'Monitor for unauthorized debug connections',
      ],
    );
  }
  
  ThreatModel _createEmulatorThreat(Map<String, dynamic> result) {
    return ThreatModel(
      id: 'emulator_detected',
      name: 'Emulator Detected',
      description: 'App is running on an emulator, which may have security vulnerabilities.',
      severity: ThreatSeverity.medium,
      category: ThreatCategory.deviceIntegrity,
      detectedAt: DateTime.now(),
      filePath: null,
      riskScore: result['riskScore'],
      recommendations: [
        'Use a physical device for better security',
        'Emulators may have security vulnerabilities',
        'Avoid sensitive operations on emulators',
      ],
    );
  }
  
  ThreatModel _createIntegrityThreat(Map<String, dynamic> result) {
    return ThreatModel(
      id: 'app_integrity_compromised',
      name: 'App Integrity Compromised',
      description: 'App signature or integrity check failed.',
      severity: ThreatSeverity.high,
      category: ThreatCategory.appIntegrity,
      detectedAt: DateTime.now(),
      filePath: null,
      riskScore: result['riskScore'],
      recommendations: [
        'Reinstall app from official store',
        'Check for app tampering',
        'Verify app source authenticity',
      ],
    );
  }
  
  ThreatModel _createPermissionThreat(Map<String, dynamic> result) {
    final dangerousPermissions = List<String>.from(result['dangerousPermissions']);
    
    return ThreatModel(
      id: 'dangerous_permissions',
      name: 'Dangerous Permissions Granted',
      description: 'App has been granted dangerous permissions that could be misused.',
      severity: ThreatSeverity.medium,
      category: ThreatCategory.permissions,
      detectedAt: DateTime.now(),
      filePath: null,
      riskScore: result['riskScore'],
      recommendations: [
        'Review and revoke unnecessary permissions',
        'Only grant permissions when needed',
        'Monitor app permission usage',
      ],
      additionalInfo: {
        'dangerous_permissions': dangerousPermissions,
      },
    );
  }
  
  List<ThreatModel> _createSystemSecurityThreats(Map<String, dynamic> result) {
    final threats = <ThreatModel>[];
    
    if (!(result['screenLockEnabled'] ?? true)) {
      threats.add(ThreatModel(
        id: 'no_screen_lock',
        name: 'No Screen Lock',
        description: 'Device does not have a screen lock configured.',
        severity: ThreatSeverity.medium,
        category: ThreatCategory.systemConfiguration,
        detectedAt: DateTime.now(),
        filePath: null,
        riskScore: 30,
        recommendations: [
          'Enable screen lock with PIN, password, or biometric',
          'Use strong authentication methods',
          'Set automatic lock timeout',
        ],
      ));
    }
    
    if (!(result['encryptionEnabled'] ?? true)) {
      threats.add(ThreatModel(
        id: 'no_encryption',
        name: 'Device Not Encrypted',
        description: 'Device storage is not encrypted.',
        severity: ThreatSeverity.high,
        category: ThreatCategory.systemConfiguration,
        detectedAt: DateTime.now(),
        filePath: null,
        riskScore: 60,
        recommendations: [
          'Enable device encryption in security settings',
          'Use full disk encryption',
          'Protect sensitive data with encryption',
        ],
      ));
    }
    
    if (result['unknownSourcesEnabled'] ?? false) {
      threats.add(ThreatModel(
        id: 'unknown_sources_enabled',
        name: 'Unknown Sources Enabled',
        description: 'Installation from unknown sources is enabled.',
        severity: ThreatSeverity.medium,
        category: ThreatCategory.systemConfiguration,
        detectedAt: DateTime.now(),
        filePath: null,
        riskScore: 25,
        recommendations: [
          'Disable installation from unknown sources',
          'Only install apps from official stores',
          'Review recently installed apps',
        ],
      ));
    }
    
    return threats;
  }
  
  ThreatModel _createNetworkThreat(Map<String, dynamic> result) {
    return ThreatModel(
      id: 'insecure_network',
      name: 'Insecure Network Connections',
      description: 'App uses insecure network connections.',
      severity: ThreatSeverity.medium,
      category: ThreatCategory.networkSecurity,
      detectedAt: DateTime.now(),
      filePath: null,
      riskScore: result['riskScore'],
      recommendations: [
        'Use HTTPS for all network communications',
        'Enable certificate pinning',
        'Avoid public WiFi for sensitive operations',
      ],
    );
  }
  
  ThreatModel _createCertificateThreat(Map<String, dynamic> result) {
    return ThreatModel(
      id: 'invalid_certificates',
      name: 'Invalid Certificates',
      description: 'Invalid or user-installed certificates detected.',
      severity: ThreatSeverity.high,
      category: ThreatCategory.certificates,
      detectedAt: DateTime.now(),
      filePath: null,
      riskScore: result['riskScore'],
      recommendations: [
        'Remove user-installed certificates',
        'Verify certificate authenticity',
        'Use certificate pinning',
      ],
    );
  }
  
  int _calculateOverallRisk(List<ThreatModel> threats) {
    if (threats.isEmpty) return 0;
    
    int totalScore = 0;
    for (final threat in threats) {
      totalScore += threat.riskScore;
    }
    
    return (totalScore / threats.length).round().clamp(0, 100);
  }
}
