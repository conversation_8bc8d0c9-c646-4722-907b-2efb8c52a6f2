import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:lottie/lottie.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/threat_indicator.dart';
import '../../../core/widgets/scan_progress_widget.dart';
import '../../security/models/security_status.dart';

class SecurityStatusCard extends StatefulWidget {
  final SecurityStatus securityStatus;
  final DateTime? lastScanTime;
  final bool isScanning;
  final VoidCallback onScanPressed;

  const SecurityStatusCard({
    super.key,
    required this.securityStatus,
    this.lastScanTime,
    required this.isScanning,
    required this.onScanPressed,
  });

  @override
  State<SecurityStatusCard> createState() => _SecurityStatusCardState();
}

class _SecurityStatusCardState extends State<SecurityStatusCard>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    if (widget.securityStatus == SecurityStatus.critical) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(SecurityStatusCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.securityStatus == SecurityStatus.critical &&
        oldWidget.securityStatus != SecurityStatus.critical) {
      _pulseController.repeat(reverse: true);
    } else if (widget.securityStatus != SecurityStatus.critical &&
        oldWidget.securityStatus == SecurityStatus.critical) {
      _pulseController.stop();
      _pulseController.reset();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          _buildStatusIndicator(),
          const SizedBox(height: 20),
          _buildStatusText(),
          const SizedBox(height: 16),
          _buildLastScanInfo(),
          const SizedBox(height: 24),
          if (widget.isScanning) _buildScanProgress() else _buildScanButton(),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.securityStatus == SecurityStatus.critical
              ? _pulseAnimation.value
              : 1.0,
          child: ThreatIndicator(
            status: widget.securityStatus,
            size: 80,
            showAnimation: widget.isScanning,
          ),
        );
      },
    );
  }

  Widget _buildStatusText() {
    final statusInfo = _getStatusInfo();
    
    return Column(
      children: [
        Text(
          statusInfo.title,
          style: AppTextStyles.headline5.copyWith(
            fontWeight: FontWeight.bold,
            color: statusInfo.color,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          statusInfo.description,
          style: AppTextStyles.body1.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLastScanInfo() {
    if (widget.lastScanTime == null) {
      return Text(
        'No previous scans',
        style: AppTextStyles.caption.copyWith(
          color: AppColors.textSecondary,
        ),
      );
    }

    final timeDifference = DateTime.now().difference(widget.lastScanTime!);
    final timeText = _formatTimeDifference(timeDifference);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          MdiIcons.clockOutline,
          size: 16,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          'Last scan: $timeText',
          style: AppTextStyles.caption.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildScanProgress() {
    return Column(
      children: [
        const ScanProgressWidget(),
        const SizedBox(height: 16),
        Text(
          'Scanning your device for threats...',
          style: AppTextStyles.body2.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildScanButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: widget.onScanPressed,
        icon: Icon(MdiIcons.magnify),
        label: const Text('Start Security Scan'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  StatusInfo _getStatusInfo() {
    switch (widget.securityStatus) {
      case SecurityStatus.secure:
        return StatusInfo(
          title: 'Device Secure',
          description: 'No threats detected. Your device is protected.',
          color: AppColors.success,
        );
      case SecurityStatus.warning:
        return StatusInfo(
          title: 'Minor Issues Found',
          description: 'Some security issues detected. Review recommended actions.',
          color: AppColors.warning,
        );
      case SecurityStatus.danger:
        return StatusInfo(
          title: 'Security Risks Detected',
          description: 'Multiple security issues found. Immediate attention required.',
          color: AppColors.error,
        );
      case SecurityStatus.critical:
        return StatusInfo(
          title: 'CRITICAL THREATS',
          description: 'Severe security threats detected. Take immediate action!',
          color: AppColors.critical,
        );
      case SecurityStatus.unknown:
        return StatusInfo(
          title: 'Security Status Unknown',
          description: 'Run a security scan to check your device status.',
          color: AppColors.textSecondary,
        );
    }
  }

  String _formatTimeDifference(Duration difference) {
    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }
}

class StatusInfo {
  final String title;
  final String description;
  final Color color;

  StatusInfo({
    required this.title,
    required this.description,
    required this.color,
  });
}
