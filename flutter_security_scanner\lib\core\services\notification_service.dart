import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:permission_handler/permission_handler.dart';

import '../models/threat_model.dart';
import '../models/notification_model.dart';
import '../utils/logger.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  static NotificationService get instance => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  
  bool _isInitialized = false;
  String? _fcmToken;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _initializeLocalNotifications();
      await _initializeFirebaseMessaging();
      await _requestPermissions();
      _isInitialized = true;
      Logger.info('NotificationService initialized successfully');
    } catch (e, stackTrace) {
      Logger.error('Failed to initialize NotificationService', e, stackTrace);
    }
  }

  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels for Android
    if (Platform.isAndroid) {
      await _createNotificationChannels();
    }
  }

  Future<void> _createNotificationChannels() async {
    const channels = [
      AndroidNotificationChannel(
        'security_alerts',
        'Security Alerts',
        description: 'Critical security threat notifications',
        importance: Importance.high,
        playSound: true,
        enableVibration: true,
      ),
      AndroidNotificationChannel(
        'scan_updates',
        'Scan Updates',
        description: 'Security scan progress and results',
        importance: Importance.defaultImportance,
        playSound: false,
      ),
      AndroidNotificationChannel(
        'system_notifications',
        'System Notifications',
        description: 'General system notifications',
        importance: Importance.low,
        playSound: false,
      ),
    ];

    for (final channel in channels) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    }
  }

  Future<void> _initializeFirebaseMessaging() async {
    try {
      // Get FCM token
      _fcmToken = await _firebaseMessaging.getToken();
      Logger.info('FCM Token: $_fcmToken');

      // Configure message handlers
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
      FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);
      FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

      // Subscribe to topics
      await _firebaseMessaging.subscribeToTopic('security_alerts');
      await _firebaseMessaging.subscribeToTopic('threat_updates');
    } catch (e) {
      Logger.warning('Firebase messaging not available: $e');
    }
  }

  Future<void> _requestPermissions() async {
    // Request notification permissions
    if (Platform.isIOS) {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );
      
      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        Logger.info('iOS notification permissions granted');
      } else {
        Logger.warning('iOS notification permissions denied');
      }
    } else if (Platform.isAndroid) {
      final status = await Permission.notification.request();
      if (status.isGranted) {
        Logger.info('Android notification permissions granted');
      } else {
        Logger.warning('Android notification permissions denied');
      }
    }
  }

  Future<void> showThreatAlert(ThreatModel threat) async {
    final notification = NotificationModel(
      id: threat.id.hashCode,
      title: _getThreatAlertTitle(threat.severity),
      body: '${threat.name}: ${threat.description}',
      channelId: 'security_alerts',
      priority: NotificationPriority.high,
      category: NotificationCategory.alarm,
      payload: {
        'type': 'threat_alert',
        'threat_id': threat.id,
        'severity': threat.severity.toString(),
      },
    );

    await _showLocalNotification(notification);
    
    // Send to server for remote notifications
    await _sendRemoteNotification(notification);
  }

  Future<void> showScanProgress({
    required String scanId,
    required double progress,
    required String currentTask,
    required int threatsFound,
  }) async {
    final notification = NotificationModel(
      id: scanId.hashCode,
      title: 'Security Scan in Progress',
      body: '$currentTask (${(progress * 100).toInt()}% complete)',
      channelId: 'scan_updates',
      priority: NotificationPriority.low,
      ongoing: true,
      progress: (progress * 100).toInt(),
      payload: {
        'type': 'scan_progress',
        'scan_id': scanId,
        'progress': progress.toString(),
        'threats_found': threatsFound.toString(),
      },
    );

    await _showLocalNotification(notification);
  }

  Future<void> showScanComplete({
    required String scanId,
    required int threatsFound,
    required Duration scanDuration,
  }) async {
    // Cancel progress notification
    await _localNotifications.cancel(scanId.hashCode);

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'Security Scan Complete',
      body: threatsFound > 0
          ? 'Found $threatsFound threat${threatsFound > 1 ? 's' : ''} in ${_formatDuration(scanDuration)}'
          : 'No threats detected. Your device is secure.',
      channelId: 'scan_updates',
      priority: threatsFound > 0 ? NotificationPriority.high : NotificationPriority.default_,
      payload: {
        'type': 'scan_complete',
        'scan_id': scanId,
        'threats_found': threatsFound.toString(),
      },
    );

    await _showLocalNotification(notification);
  }

  Future<void> showFileChangeAlert({
    required String filePath,
    required String changeType,
    required String severity,
  }) async {
    final notification = NotificationModel(
      id: filePath.hashCode,
      title: 'File System Change Detected',
      body: '$changeType: ${filePath.split('/').last}',
      channelId: severity == 'high' ? 'security_alerts' : 'system_notifications',
      priority: severity == 'high' ? NotificationPriority.high : NotificationPriority.default_,
      payload: {
        'type': 'file_change',
        'file_path': filePath,
        'change_type': changeType,
        'severity': severity,
      },
    );

    await _showLocalNotification(notification);
  }

  Future<void> showSystemAlert({
    required String title,
    required String message,
    NotificationPriority priority = NotificationPriority.high,
    Map<String, String>? payload,
  }) async {
    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch,
      title: title,
      body: message,
      channelId: 'security_alerts',
      priority: priority,
      payload: payload ?? {'type': 'system_alert'},
    );

    await _showLocalNotification(notification);
  }

  Future<void> _showLocalNotification(NotificationModel notification) async {
    try {
      final androidDetails = AndroidNotificationDetails(
        notification.channelId,
        _getChannelName(notification.channelId),
        channelDescription: _getChannelDescription(notification.channelId),
        importance: _getAndroidImportance(notification.priority),
        priority: _getAndroidPriority(notification.priority),
        playSound: notification.priority == NotificationPriority.high,
        enableVibration: notification.priority == NotificationPriority.high,
        ongoing: notification.ongoing,
        progress: notification.progress,
        maxProgress: notification.progress != null ? 100 : null,
        showProgress: notification.progress != null,
        category: _getAndroidCategory(notification.category),
        color: _getNotificationColor(notification.priority),
        icon: '@drawable/ic_security_notification',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        interruptionLevel: InterruptionLevel.active,
      );

      final details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        notification.id,
        notification.title,
        notification.body,
        details,
        payload: notification.payload != null ? _encodePayload(notification.payload!) : null,
      );
    } catch (e, stackTrace) {
      Logger.error('Failed to show local notification', e, stackTrace);
    }
  }

  Future<void> _sendRemoteNotification(NotificationModel notification) async {
    try {
      // This would typically send to your backend API
      // which then sends push notifications to other devices
      Logger.info('Sending remote notification: ${notification.title}');
    } catch (e) {
      Logger.warning('Failed to send remote notification: $e');
    }
  }

  void _onNotificationTapped(NotificationResponse response) {
    try {
      final payload = response.payload != null ? _decodePayload(response.payload!) : null;
      
      if (payload != null) {
        _handleNotificationAction(payload);
      }
    } catch (e) {
      Logger.error('Error handling notification tap', e);
    }
  }

  void _handleForegroundMessage(RemoteMessage message) {
    Logger.info('Received foreground message: ${message.messageId}');
    
    // Show local notification for foreground messages
    final notification = NotificationModel(
      id: message.hashCode,
      title: message.notification?.title ?? 'Security Alert',
      body: message.notification?.body ?? 'New security notification',
      channelId: 'security_alerts',
      priority: NotificationPriority.high,
      payload: message.data,
    );
    
    _showLocalNotification(notification);
  }

  void _handleMessageOpenedApp(RemoteMessage message) {
    Logger.info('Message opened app: ${message.messageId}');
    _handleNotificationAction(message.data);
  }

  void _handleNotificationAction(Map<String, dynamic> payload) {
    final type = payload['type'] as String?;
    
    switch (type) {
      case 'threat_alert':
        _navigateToThreatDetails(payload['threat_id']);
        break;
      case 'scan_complete':
        _navigateToScanResults(payload['scan_id']);
        break;
      case 'file_change':
        _navigateToFileChanges();
        break;
      default:
        _navigateToHome();
    }
  }

  void _navigateToThreatDetails(String? threatId) {
    // Navigate to threat details screen
    Logger.info('Navigate to threat details: $threatId');
  }

  void _navigateToScanResults(String? scanId) {
    // Navigate to scan results screen
    Logger.info('Navigate to scan results: $scanId');
  }

  void _navigateToFileChanges() {
    // Navigate to file changes screen
    Logger.info('Navigate to file changes');
  }

  void _navigateToHome() {
    // Navigate to home screen
    Logger.info('Navigate to home');
  }

  String _getThreatAlertTitle(ThreatSeverity severity) {
    switch (severity) {
      case ThreatSeverity.critical:
        return '🚨 CRITICAL SECURITY ALERT';
      case ThreatSeverity.high:
        return '⚠️ High Risk Threat Detected';
      case ThreatSeverity.medium:
        return '⚠️ Security Warning';
      case ThreatSeverity.low:
        return 'ℹ️ Security Notice';
    }
  }

  String _getChannelName(String channelId) {
    switch (channelId) {
      case 'security_alerts':
        return 'Security Alerts';
      case 'scan_updates':
        return 'Scan Updates';
      case 'system_notifications':
        return 'System Notifications';
      default:
        return 'General';
    }
  }

  String _getChannelDescription(String channelId) {
    switch (channelId) {
      case 'security_alerts':
        return 'Critical security threat notifications';
      case 'scan_updates':
        return 'Security scan progress and results';
      case 'system_notifications':
        return 'General system notifications';
      default:
        return 'General notifications';
    }
  }

  Importance _getAndroidImportance(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.high:
        return Importance.high;
      case NotificationPriority.default_:
        return Importance.defaultImportance;
      case NotificationPriority.low:
        return Importance.low;
    }
  }

  Priority _getAndroidPriority(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.high:
        return Priority.high;
      case NotificationPriority.default_:
        return Priority.defaultPriority;
      case NotificationPriority.low:
        return Priority.low;
    }
  }

  AndroidNotificationCategory? _getAndroidCategory(NotificationCategory? category) {
    switch (category) {
      case NotificationCategory.alarm:
        return AndroidNotificationCategory.alarm;
      case NotificationCategory.call:
        return AndroidNotificationCategory.call;
      case NotificationCategory.message:
        return AndroidNotificationCategory.message;
      case NotificationCategory.progress:
        return AndroidNotificationCategory.progress;
      case null:
        return null;
    }
  }

  Color _getNotificationColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.high:
        return Colors.red;
      case NotificationPriority.default_:
        return Colors.blue;
      case NotificationPriority.low:
        return Colors.grey;
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  String _encodePayload(Map<String, dynamic> payload) {
    return payload.entries.map((e) => '${e.key}=${e.value}').join('&');
  }

  Map<String, dynamic> _decodePayload(String payload) {
    final result = <String, dynamic>{};
    for (final pair in payload.split('&')) {
      final parts = pair.split('=');
      if (parts.length == 2) {
        result[parts[0]] = parts[1];
      }
    }
    return result;
  }

  Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }

  Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  String? get fcmToken => _fcmToken;
}

@pragma('vm:entry-point')
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  Logger.info('Handling background message: ${message.messageId}');
  // Handle background message
}
