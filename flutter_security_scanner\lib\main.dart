import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:workmanager/workmanager.dart';

import 'core/app_config.dart';
import 'core/theme/app_theme.dart';
import 'core/router/app_router.dart';
import 'core/services/notification_service.dart';
import 'core/services/security_service.dart';
import 'core/services/background_service.dart';
import 'core/utils/logger.dart';
import 'features/security/models/threat_model.dart';
import 'features/security/models/scan_result_model.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp();
  
  // Initialize Hive
  await Hive.initFlutter();
  Hive.registerAdapter(ThreatModelAdapter());
  Hive.registerAdapter(ScanResultModelAdapter());
  
  // Initialize services
  await _initializeServices();
  
  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );
  
  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  runApp(const ProviderScope(child: SecurityScannerApp()));
}

Future<void> _initializeServices() async {
  try {
    // Initialize logger
    Logger.init();
    
    // Initialize notification service
    await NotificationService.instance.initialize();
    
    // Initialize security service
    await SecurityService.instance.initialize();
    
    // Initialize background service
    await BackgroundService.instance.initialize();
    
    // Initialize work manager for background tasks
    Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: AppConfig.isDebugMode,
    );
    
    Logger.info('All services initialized successfully');
  } catch (e, stackTrace) {
    Logger.error('Failed to initialize services', e, stackTrace);
  }
}

@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      switch (task) {
        case 'security_scan':
          await BackgroundService.instance.performBackgroundScan();
          break;
        case 'file_monitor':
          await BackgroundService.instance.monitorFileChanges();
          break;
        case 'threat_update':
          await BackgroundService.instance.updateThreatDatabase();
          break;
        default:
          Logger.warning('Unknown background task: $task');
      }
      return Future.value(true);
    } catch (e, stackTrace) {
      Logger.error('Background task failed: $task', e, stackTrace);
      return Future.value(false);
    }
  });
}

class SecurityScannerApp extends ConsumerWidget {
  const SecurityScannerApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);
    
    return MaterialApp.router(
      title: 'Security Scanner',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      routerConfig: router,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: TextScaler.linear(
              MediaQuery.of(context).textScaleFactor.clamp(0.8, 1.2),
            ),
          ),
          child: child!,
        );
      },
    );
  }
}
