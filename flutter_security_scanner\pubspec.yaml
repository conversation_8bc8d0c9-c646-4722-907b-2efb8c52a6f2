name: security_scanner
description: A comprehensive mobile security scanner for Android and iOS devices.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Design
  cupertino_icons: ^1.0.6
  material_design_icons_flutter: ^7.0.7296
  flutter_svg: ^2.0.9
  lottie: ^2.7.0
  shimmer: ^3.0.0
  
  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # Navigation
  go_router: ^12.1.3
  
  # Network & API
  http: ^1.1.2
  dio: ^5.4.0
  connectivity_plus: ^5.0.2
  
  # Local Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.1
  
  # Security & Encryption
  crypto: ^3.0.3
  encrypt: ^5.0.1
  local_auth: ^2.1.7
  flutter_secure_storage: ^9.0.0
  
  # Device & Platform
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  permission_handler: ^11.1.0
  flutter_jailbreak_detection: ^1.10.0
  root_detector: ^1.0.0
  
  # File System
  path: ^1.8.3
  file_picker: ^6.1.1
  open_file: ^3.3.2
  
  # Background Tasks
  workmanager: ^0.5.2
  flutter_background_service: ^5.0.5
  
  # Notifications
  flutter_local_notifications: ^16.3.2
  firebase_messaging: ^14.7.10
  
  # Analytics & Monitoring
  firebase_core: ^2.24.2
  firebase_analytics: ^10.7.4
  firebase_crashlytics: ^3.4.8
  
  # Utils
  intl: ^0.19.0
  uuid: ^4.2.1
  url_launcher: ^6.2.2
  share_plus: ^7.2.1
  
  # Charts & Visualization
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^23.2.7
  
  # Platform Specific
  android_intent_plus: ^4.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/data/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
    - family: SecurityIcons
      fonts:
        - asset: assets/fonts/SecurityIcons.ttf

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 24
  
  adaptive_icon_background: "#1976D2"
  adaptive_icon_foreground: "assets/icons/app_icon_foreground.png"
