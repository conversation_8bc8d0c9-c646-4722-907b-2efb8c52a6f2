import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:lottie/lottie.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/threat_indicator.dart';
import '../../../core/widgets/scan_progress_widget.dart';
import '../providers/dashboard_provider.dart';
import '../widgets/security_status_card.dart';
import '../widgets/quick_actions_grid.dart';
import '../widgets/threat_summary_card.dart';
import '../widgets/recent_scans_list.dart';
import '../../security/models/threat_model.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dashboardState = ref.watch(dashboardProvider);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: RefreshIndicator(
            onRefresh: () => ref.read(dashboardProvider.notifier).refreshData(),
            child: CustomScrollView(
              slivers: [
                _buildAppBar(context),
                SliverPadding(
                  padding: const EdgeInsets.all(16.0),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      _buildSecurityStatusSection(dashboardState),
                      const SizedBox(height: 20),
                      _buildQuickActionsSection(),
                      const SizedBox(height: 20),
                      _buildThreatSummarySection(dashboardState),
                      const SizedBox(height: 20),
                      _buildRecentScansSection(dashboardState),
                      const SizedBox(height: 20),
                      _buildDeviceInfoSection(dashboardState),
                      const SizedBox(height: 100), // Bottom padding for FAB
                    ]),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 120.0,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Security Scanner',
          style: AppTextStyles.headline6.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary,
                AppColors.primaryDark,
              ],
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                right: -50,
                top: -50,
                child: Icon(
                  MdiIcons.shieldCheck,
                  size: 200,
                  color: Colors.white.withOpacity(0.1),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.settings, color: Colors.white),
          onPressed: () => _navigateToSettings(context),
        ),
        IconButton(
          icon: const Icon(Icons.notifications, color: Colors.white),
          onPressed: () => _navigateToNotifications(context),
        ),
      ],
    );
  }

  Widget _buildSecurityStatusSection(DashboardState state) {
    return SecurityStatusCard(
      securityStatus: state.securityStatus,
      lastScanTime: state.lastScanTime,
      isScanning: state.isScanning,
      onScanPressed: () => _startQuickScan(),
    );
  }

  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTextStyles.headline6.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        const QuickActionsGrid(),
      ],
    );
  }

  Widget _buildThreatSummarySection(DashboardState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Threat Summary',
              style: AppTextStyles.headline6.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => _navigateToThreats(context),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ThreatSummaryCard(
          criticalCount: state.threatSummary.critical,
          highCount: state.threatSummary.high,
          mediumCount: state.threatSummary.medium,
          lowCount: state.threatSummary.low,
        ),
      ],
    );
  }

  Widget _buildRecentScansSection(DashboardState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Scans',
              style: AppTextStyles.headline6.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => _navigateToScanHistory(context),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        RecentScansList(
          scans: state.recentScans,
          onScanTapped: (scan) => _navigateToScanDetails(context, scan),
        ),
      ],
    );
  }

  Widget _buildDeviceInfoSection(DashboardState state) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                MdiIcons.cellphone,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Device Information',
                style: AppTextStyles.subtitle1.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (state.deviceInfo != null) ...[
            _buildDeviceInfoRow('Platform', state.deviceInfo!.platform),
            _buildDeviceInfoRow('Model', state.deviceInfo!.model),
            _buildDeviceInfoRow('OS Version', state.deviceInfo!.osVersion),
            _buildDeviceInfoRow('App Version', state.deviceInfo!.appVersion),
            _buildDeviceInfoRow(
              'Security Status',
              state.deviceInfo!.isPhysicalDevice ? 'Physical Device' : 'Emulator',
            ),
          ] else
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }

  Widget _buildDeviceInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.body2.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: AppTextStyles.body2.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return Consumer(
      builder: (context, ref, child) {
        final isScanning = ref.watch(dashboardProvider).isScanning;
        
        return FloatingActionButton.extended(
          onPressed: isScanning ? null : _startEmergencyScan,
          backgroundColor: isScanning ? AppColors.disabled : AppColors.error,
          icon: isScanning
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Icon(MdiIcons.shieldAlert),
          label: Text(
            isScanning ? 'Scanning...' : 'Emergency Scan',
            style: const TextStyle(color: Colors.white),
          ),
        );
      },
    );
  }

  void _startQuickScan() {
    ref.read(dashboardProvider.notifier).startQuickScan();
  }

  void _startEmergencyScan() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Emergency Security Scan'),
        content: const Text(
          'This will perform a comprehensive security scan of your device. '
          'The scan may take several minutes to complete.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(dashboardProvider.notifier).startEmergencyScan();
            },
            child: const Text('Start Scan'),
          ),
        ],
      ),
    );
  }

  void _navigateToSettings(BuildContext context) {
    // Navigate to settings screen
  }

  void _navigateToNotifications(BuildContext context) {
    // Navigate to notifications screen
  }

  void _navigateToThreats(BuildContext context) {
    // Navigate to threats screen
  }

  void _navigateToScanHistory(BuildContext context) {
    // Navigate to scan history screen
  }

  void _navigateToScanDetails(BuildContext context, dynamic scan) {
    // Navigate to scan details screen
  }
}
